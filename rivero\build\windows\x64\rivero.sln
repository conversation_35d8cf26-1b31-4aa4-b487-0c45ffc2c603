﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{C2ECE31B-DC4A-306C-BF8F-3EEF4BA5DDEC}"
	ProjectSection(ProjectDependencies) = postProject
		{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9} = {6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}
		{EED505C9-0968-30D2-9274-0854B456A4ED} = {EED505C9-0968-30D2-9274-0854B456A4ED}
		{70DE3D6F-0758-38C8-AB81-D1CAA21415F6} = {70DE3D6F-0758-38C8-AB81-D1CAA21415F6}
		{A14CD568-6982-355A-B3FD-724CD888AD49} = {A14CD568-6982-355A-B3FD-724CD888AD49}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{AA819BF2-DB25-3F07-8C42-20F7B0173A87}"
	ProjectSection(ProjectDependencies) = postProject
		{C2ECE31B-DC4A-306C-BF8F-3EEF4BA5DDEC} = {C2ECE31B-DC4A-306C-BF8F-3EEF4BA5DDEC}
		{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9} = {6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "flutter\flutter_assemble.vcxproj", "{A587EFB9-B344-325B-ACFC-0092394CB56F}"
	ProjectSection(ProjectDependencies) = postProject
		{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9} = {6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_app", "flutter\flutter_wrapper_app.vcxproj", "{EED505C9-0968-30D2-9274-0854B456A4ED}"
	ProjectSection(ProjectDependencies) = postProject
		{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9} = {6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}
		{A587EFB9-B344-325B-ACFC-0092394CB56F} = {A587EFB9-B344-325B-ACFC-0092394CB56F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "flutter\flutter_wrapper_plugin.vcxproj", "{70DE3D6F-0758-38C8-AB81-D1CAA21415F6}"
	ProjectSection(ProjectDependencies) = postProject
		{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9} = {6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}
		{A587EFB9-B344-325B-ACFC-0092394CB56F} = {A587EFB9-B344-325B-ACFC-0092394CB56F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "rivero", "runner\rivero.vcxproj", "{A14CD568-6982-355A-B3FD-724CD888AD49}"
	ProjectSection(ProjectDependencies) = postProject
		{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9} = {6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}
		{A587EFB9-B344-325B-ACFC-0092394CB56F} = {A587EFB9-B344-325B-ACFC-0092394CB56F}
		{EED505C9-0968-30D2-9274-0854B456A4ED} = {EED505C9-0968-30D2-9274-0854B456A4ED}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C2ECE31B-DC4A-306C-BF8F-3EEF4BA5DDEC}.Debug|x64.ActiveCfg = Debug|x64
		{C2ECE31B-DC4A-306C-BF8F-3EEF4BA5DDEC}.Debug|x64.Build.0 = Debug|x64
		{C2ECE31B-DC4A-306C-BF8F-3EEF4BA5DDEC}.Profile|x64.ActiveCfg = Profile|x64
		{C2ECE31B-DC4A-306C-BF8F-3EEF4BA5DDEC}.Profile|x64.Build.0 = Profile|x64
		{C2ECE31B-DC4A-306C-BF8F-3EEF4BA5DDEC}.Release|x64.ActiveCfg = Release|x64
		{C2ECE31B-DC4A-306C-BF8F-3EEF4BA5DDEC}.Release|x64.Build.0 = Release|x64
		{AA819BF2-DB25-3F07-8C42-20F7B0173A87}.Debug|x64.ActiveCfg = Debug|x64
		{AA819BF2-DB25-3F07-8C42-20F7B0173A87}.Debug|x64.Build.0 = Debug|x64
		{AA819BF2-DB25-3F07-8C42-20F7B0173A87}.Profile|x64.ActiveCfg = Profile|x64
		{AA819BF2-DB25-3F07-8C42-20F7B0173A87}.Profile|x64.Build.0 = Profile|x64
		{AA819BF2-DB25-3F07-8C42-20F7B0173A87}.Release|x64.ActiveCfg = Release|x64
		{AA819BF2-DB25-3F07-8C42-20F7B0173A87}.Release|x64.Build.0 = Release|x64
		{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}.Debug|x64.ActiveCfg = Debug|x64
		{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}.Debug|x64.Build.0 = Debug|x64
		{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}.Profile|x64.ActiveCfg = Profile|x64
		{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}.Profile|x64.Build.0 = Profile|x64
		{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}.Release|x64.ActiveCfg = Release|x64
		{6B31E478-5EE5-3F45-A41E-D1563EE9BEC9}.Release|x64.Build.0 = Release|x64
		{A587EFB9-B344-325B-ACFC-0092394CB56F}.Debug|x64.ActiveCfg = Debug|x64
		{A587EFB9-B344-325B-ACFC-0092394CB56F}.Debug|x64.Build.0 = Debug|x64
		{A587EFB9-B344-325B-ACFC-0092394CB56F}.Profile|x64.ActiveCfg = Profile|x64
		{A587EFB9-B344-325B-ACFC-0092394CB56F}.Profile|x64.Build.0 = Profile|x64
		{A587EFB9-B344-325B-ACFC-0092394CB56F}.Release|x64.ActiveCfg = Release|x64
		{A587EFB9-B344-325B-ACFC-0092394CB56F}.Release|x64.Build.0 = Release|x64
		{EED505C9-0968-30D2-9274-0854B456A4ED}.Debug|x64.ActiveCfg = Debug|x64
		{EED505C9-0968-30D2-9274-0854B456A4ED}.Debug|x64.Build.0 = Debug|x64
		{EED505C9-0968-30D2-9274-0854B456A4ED}.Profile|x64.ActiveCfg = Profile|x64
		{EED505C9-0968-30D2-9274-0854B456A4ED}.Profile|x64.Build.0 = Profile|x64
		{EED505C9-0968-30D2-9274-0854B456A4ED}.Release|x64.ActiveCfg = Release|x64
		{EED505C9-0968-30D2-9274-0854B456A4ED}.Release|x64.Build.0 = Release|x64
		{70DE3D6F-0758-38C8-AB81-D1CAA21415F6}.Debug|x64.ActiveCfg = Debug|x64
		{70DE3D6F-0758-38C8-AB81-D1CAA21415F6}.Debug|x64.Build.0 = Debug|x64
		{70DE3D6F-0758-38C8-AB81-D1CAA21415F6}.Profile|x64.ActiveCfg = Profile|x64
		{70DE3D6F-0758-38C8-AB81-D1CAA21415F6}.Profile|x64.Build.0 = Profile|x64
		{70DE3D6F-0758-38C8-AB81-D1CAA21415F6}.Release|x64.ActiveCfg = Release|x64
		{70DE3D6F-0758-38C8-AB81-D1CAA21415F6}.Release|x64.Build.0 = Release|x64
		{A14CD568-6982-355A-B3FD-724CD888AD49}.Debug|x64.ActiveCfg = Debug|x64
		{A14CD568-6982-355A-B3FD-724CD888AD49}.Debug|x64.Build.0 = Debug|x64
		{A14CD568-6982-355A-B3FD-724CD888AD49}.Profile|x64.ActiveCfg = Profile|x64
		{A14CD568-6982-355A-B3FD-724CD888AD49}.Profile|x64.Build.0 = Profile|x64
		{A14CD568-6982-355A-B3FD-724CD888AD49}.Release|x64.ActiveCfg = Release|x64
		{A14CD568-6982-355A-B3FD-724CD888AD49}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D6435EC5-0D2B-3D32-8924-DEDE15DF1231}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
